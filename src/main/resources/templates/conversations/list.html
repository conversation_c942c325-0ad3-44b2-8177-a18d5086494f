<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Conversations</title>
    <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .chat-list {
            max-width: 800px;
            margin: 0 auto;
        }

        .sidebar {
            width: 300px;
            position: fixed;
            left: 0;
            top: 60px; /* Height of the header */
            bottom: 0;
            background: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .main-content {
            margin-left: 320px;
            padding: 20px;
            margin-top: 60px; /* Height of the header */
        }

        .chat-item {
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            margin-bottom: 10px;
            padding: 12px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .chat-item:hover {
            transform: translateX(5px);
            background: #e9ecef;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .chat-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .chat-time {
            font-size: 0.85em;
            color: #6c757d;
        }

        .chat-preview {
            margin-top: 5px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 15px 0;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            right: 0;
            left: 0; /* Changed from 300px to 0 to span full width */
            z-index: 999;
            height: 60px; /* Fixed height for the header */
        }

        .header h2 {
            margin: 0;
            font-size: 1.75rem;
        }

        .bot-type {
            display: inline-block;
            padding: 2px 6px;
            background: #e3f2fd;
            color: #0d6efd;
            border-radius: 4px;
            font-size: 0.85em;
        }

        .conversation-type {
            font-weight: 500;
            padding: 2px 6px;
            font-size: 0.85em;
        }

        .empty-state {
            text-align: center;
            padding: 30px 20px;
            color: #6c757d;
        }

        .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
        }

        .page-link {
            color: #007bff;
        }

        .page-link:hover {
            color: #0056b3;
        }

        .user-icon {
            background: #e3f2fd;
            color: #007bff;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 0.9em;
        }

        .chat-header {
            display: flex;
            align-items: center;
        }

        .pagination-container {
            margin-top: 15px;
            margin-bottom: 15px;
        }

        .page-link {
            padding: 0.375rem 0.75rem;
        }

        .chat-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            max-height: 150px;
            overflow-y: auto;
        }

        .chat-content p {
            margin: 0;
            color: #212529;
        }

        .badge.bg-danger {
            padding: 6px 12px;
            font-weight: 500;
            font-size: 0.85em;
            border-radius: 4px;
            background-color: #dc3545 !important;
        }

        audio {
            height: 35px;
            margin: 8px 0;
        }

        .badge.bg-danger {
            padding: 5px 10px;
            font-weight: normal;
        }
    </style>
</head>
<body>
<div class="sidebar">
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Search Conversations</h5>
        </div>
        <div class="card-body">
            <form action="/web/admin/conversations" method="get" class="row g-3">
                <div class="col-12">
                    <label for="id" class="form-label">Conversation ID</label>
                    <input
                            type="number"
                            class="form-control"
                            id="id"
                            name="id"
                            th:value="${id}"
                            placeholder="Enter ID"
                    />
                </div>
                <div class="col-12">
                    <label for="phone" class="form-label">Phone</label>
                    <input
                            type="text"
                            class="form-control"
                            id="phone"
                            name="phone"
                            th:value="${phone}"
                            placeholder="Enter phone"
                    />
                </div>
                <div class="col-12">
                    <label for="userId" class="form-label">User ID</label>
                    <input
                            type="text"
                            class="form-control"
                            id="userId"
                            name="userId"
                            th:value="${userId}"
                            placeholder="Enter user ID"
                    />
                </div>
                <div class="col-12">
                    <label for="botId" class="form-label">Bot ID</label>
                    <input
                            type="number"
                            class="form-control"
                            id="botId"
                            name="botId"
                            th:value="${botId}"
                            placeholder="Enter bot ID"
                    />
                </div>
                <div class="col-12">
                    <label for="startDate" class="form-label">Start Date</label>
                    <input
                            type="date"
                            class="form-control"
                            id="startDate"
                            name="startDate"
                            th:value="${startDate}"
                    />
                </div>
                <div class="col-12">
                    <label for="endDate" class="form-label">End Date</label>
                    <input
                            type="date"
                            class="form-control"
                            id="endDate"
                            name="endDate"
                            th:value="${endDate}"
                    />
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        Search
                    </button>
                    <a href="/web/admin/conversations" class="btn btn-secondary w-100"
                    >Reset</a
                    >
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Other Functions</h5>
        </div>
        <div class="card-body">
            <div class="d-flex flex-column gap-2">
                <button id="filterReported" class="btn btn-primary w-100">
                    <i class="bi bi-exclamation-triangle"></i> Filter Report
                </button>
                <button id="viewProfile" class="btn btn-info w-100">
                    <i class="bi bi-person-badge"></i> View Profile
                </button>
                <button id="generateReport" class="btn btn-success w-100">
                    <i class="bi bi-file-text"></i> Report from messages
                </button>
            </div>
        </div>
    </div>
</div>

<div class="main-content">
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-chat-dots"></i> Chat History</h2>
                <a href="/web/admin/dashboard" class="btn btn-light">
                    <i class="bi bi-house-door"></i> Back to Home
                </a>
            </div>
        </div>
    </div>

    <div class="container mt-5">
        <div class="chat-list">
            <div th:if="${conversations.empty}" class="empty-state">
                <i class="bi bi-chat-square-dots" style="font-size: 3rem"></i>
                <h4 class="mt-3">No conversations yet</h4>
                <p>When you start chatting, your conversations will appear here.</p>
            </div>

            <div
                    th:if="${conv != null}"
                    th:each="conv : ${conversations.content}"
                    class="chat-item"
                    th:onclick="'window.location.href=\'' + @{'/web/admin/conversations/' + ${conv.id}} + '\''"
            >
                <div class="chat-header">
                    <div class="user-icon">
                        <i class="bi bi-person"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <div
                                    class="chat-name"
                                    th:text="'User ' + ${conv?.userId}"
                            ></div>
                            <div
                                    class="chat-time"
                                    th:text="${#dates.format(conv?.createdAt, 'dd-MM-yyyy HH:mm')}"
                            >
                                <i class="bi bi-clock ms-1"></i>
                            </div>
                        </div>
                        <div class="chat-preview">
                  <span class="bot-type" th:if="${conv?.botType != null}">
                    <i class="bi bi-robot"></i>
                    <span th:text="${conv.botType}"></span>
                  </span>
                            <span
                                    class="badge bg-primary conversation-type"
                                    th:if="${conv?.botId != null}"
                                    th:text="'Bot ' + ${conv.botId}"
                            >
                  </span>
                            <span
                                    class="badge bg-primary conversation-type"
                                    th:if="${conv?.asrType != null}"
                                    th:text="'STT ' + ${conv.asrType}"
                            >
                  </span>
                            <span
                                    class="badge bg-primary conversation-type"
                                    th:if="${conv?.phone != null}"
                                    th:text="'Phone ' + ${conv.phone}"
                                    th:attr="data-phone=${conv.phone}"
                            >
                  </span>
                            <span
                                    class="badge bg-primary conversation-type"
                                    th:if="${conv?.id != null}"
                                    th:text="'id ' + ${conv.id}"
                            >
                  </span>
                            <span
                                    class="badge bg-primary conversation-type"
                                    th:if="${conv?.video != null}"
                                    th:text="'video'"
                            >
                  </span>
                        </div>
                    </div>
                </div>
            </div>

            <nav
                    th:if="${!conversations.empty}"
                    class="d-flex justify-content-center pagination-container"
            >
                <ul class="pagination">
                    <li
                            class="page-item"
                            th:classappend="${conversations.first} ? 'disabled'"
                    >
                        <a
                                class="page-link"
                                th:href="@{/web/admin/conversations(page=${conversations.number - 1}, id=${id}, phone=${phone}, userId=${userId}, startDate=${startDate}, endDate=${endDate}, botId=${botId})}"
                        >
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>

                    <li
                            class="page-item"
                            th:classappend="${conversations.number == 0} ? 'active'"
                    >
                        <a
                                class="page-link"
                                th:href="@{/web/admin/conversations(page=0, id=${id}, phone=${phone}, userId=${userId}, startDate=${startDate}, endDate=${endDate}, botId=${botId})}"
                        >1</a
                        >
                    </li>

                    <li
                            class="page-item disabled"
                            th:if="${conversations.number > 3}"
                    >
                        <span class="page-link">...</span>
                    </li>

                    <li
                            class="page-item"
                            th:each="i : ${#numbers.sequence(1, conversations.totalPages - 1)}"
                            th:if="${i > 0 && i < conversations.totalPages - 1 &&
                       (i <= 5 ||
                        i >= conversations.number - 2 && i <= conversations.number + 2)}"
                            th:classappend="${i == conversations.number} ? 'active'"
                    >
                        <a
                                class="page-link"
                                th:href="@{/web/admin/conversations(page=${i}, id=${id}, phone=${phone}, userId=${userId}, startDate=${startDate}, endDate=${endDate}, botId=${botId})}"
                                th:text="${i + 1}"
                        ></a>
                    </li>

                    <li
                            class="page-item disabled"
                            th:if="${conversations.number < conversations.totalPages - 4}"
                    >
                        <span class="page-link">...</span>
                    </li>

                    <li
                            class="page-item"
                            th:if="${conversations.totalPages > 1}"
                            th:classappend="${conversations.number == conversations.totalPages - 1} ? 'active'"
                    >
                        <a
                                class="page-link"
                                th:href="@{/web/admin/conversations(page=${conversations.totalPages - 1}, id=${id}, phone=${phone}, userId=${userId}, startDate=${startDate}, endDate=${endDate}, botId=${botId})}"
                                th:text="${conversations.totalPages}"
                        ></a>
                    </li>

                    <li
                            class="page-item"
                            th:classappend="${conversations.last} ? 'disabled'"
                    >
                        <a
                                class="page-link"
                                th:href="@{/web/admin/conversations(page=${conversations.number + 1}, id=${id}, phone=${phone}, userId=${userId}, startDate=${startDate}, endDate=${endDate}, botId=${botId})}"
                        >
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Add Modal for Profile Info -->
<div class="modal fade" id="profileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Robot Profile Information</h5>
                <button
                        type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                ></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="robotIdInput" class="form-label">User ID</label>
                    <input
                            type="text"
                            class="form-control"
                            id="robotIdInput"
                            placeholder="Enter User ID"
                    />
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>Key</th>
                            <th>Value</th>
                        </tr>
                        </thead>
                        <tbody id="profileInfoBody"></tbody>
                    </table>
                </div>
                <div id="profileLoading" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <button
                                type="button"
                                class="btn btn-warning"
                                id="editProfileBtn"
                                onclick="toggleEditMode()"
                                style="display: none;"
                        >
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                        <button
                                type="button"
                                class="btn btn-success"
                                id="updateProfileBtn"
                                onclick="updateProfile()"
                                style="display: none;"
                        >
                            <i class="bi bi-check-circle"></i> Update
                        </button>
                    </div>
                    <div>
                        <button
                                type="button"
                                class="btn btn-secondary"
                                data-bs-dismiss="modal"
                        >
                            Close
                        </button>
                        <button
                                type="button"
                                class="btn btn-primary"
                                onclick="fetchProfileInfo()"
                        >
                            View Profile
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Modal for Report Generation -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Generate Report from Messages</h5>
                <button
                        type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                ></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="conversationIdInput" class="form-label">Conversation ID</label>
                    <input
                            type="number"
                            class="form-control"
                            id="conversationIdInput"
                            value="0"
                            placeholder="Enter conversation ID"
                    />
                </div>
                <div class="mb-3">
                    <label for="reportListInput" class="form-label">Enter messages as JSON array</label>
                    <textarea
                            class="form-control"
                            id="reportListInput"
                            rows="10"
                            placeholder='["message 1", "message 2", "message 3"]'
                    ></textarea>
                </div>
                <div id="reportLoading" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div id="reportResult" class="alert alert-success d-none">
                    Report generated successfully!
                </div>
                <div id="reportResultContent" class="mt-4 d-none">
                    <h5>Report Content:</h5>
                    <div id="reportContentDisplay" class="p-3 border rounded bg-light">
                    </div>
                </div>
                <div id="reportError" class="alert alert-danger d-none">
                    Error generating report. Please try again.
                </div>
            </div>
            <div class="modal-footer">
                <button
                        type="button"
                        class="btn btn-secondary"
                        data-bs-dismiss="modal"
                >
                    Close
                </button>
                <button
                        type="button"
                        class="btn btn-primary"
                        id="submitReportBtn"
                        onclick="generateReportFromList()"
                >
                    Generate Report
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let isShowingReported = false;
    const profileModal = new bootstrap.Modal(
        document.getElementById("profileModal")
    );
    const reportModal = new bootstrap.Modal(
        document.getElementById("reportModal")
    );

    // Function to mask the first 5 digits of a phone number
    function maskPhoneNumber(phone) {
        if (!phone) return "";
        // Handle phone numbers that start with a plus sign
        return phone
            .toString()
            .replace(/^(\+?\d{0,5})(.*)/, function (match, p1, p2) {
                // If the phone starts with +, keep the + and mask the next 5 characters
                if (p1.startsWith("+")) {
                    return "+" + "*".repeat(Math.min(5, p1.length - 1)) + p2;
                }
                // Otherwise mask the first 5 characters
                return "*".repeat(Math.min(5, p1.length)) + p2;
            });
    }

    // Apply phone masking to all phone badges when the page loads
    document.addEventListener("DOMContentLoaded", function () {
        const phoneElements = document.querySelectorAll("[data-phone]");
        phoneElements.forEach((element) => {
            const phone = element.getAttribute("data-phone");
            element.textContent = "Phone " + maskPhoneNumber(phone);
        });

        // Add event listener to the search form to mask phone numbers in the results
        const searchForm = document.querySelector(
            'form[action="/web/admin/conversations"]'
        );
        if (searchForm) {
            searchForm.addEventListener("submit", function (event) {
                // Don't mask the input field itself, as that would affect the search
                // The masking will be applied to the results after they load
            });
        }
    });

    function loadOriginalConversations() {
        // Get current search parameters
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get("id");
        const phone = urlParams.get("phone");
        const userId = urlParams.get("userId");
        const startDate = urlParams.get("startDate");
        const endDate = urlParams.get("endDate");
        const botId = urlParams.get("botId");
        const page = urlParams.get("page") || 0;

        // Construct URL with search parameters
        let url = `/web/admin/conversations?page=${page}`;
        if (id) url += `&id=${id}`;
        if (phone) url += `&phone=${phone}`;
        if (userId) url += `&userId=${userId}`;
        if (startDate) url += `&startDate=${startDate}`;
        if (endDate) url += `&endDate=${endDate}`;
        if (botId) url += `&botId=${botId}`;

        window.location.href = url;
    }

    function loadReportedConversations(page = 0) {
        // Get current search parameters
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get("id");
        const phone = urlParams.get("phone");
        const userId = urlParams.get("userId");
        const startDate = urlParams.get("startDate");
        const endDate = urlParams.get("endDate");

        fetch(`/robot/api/v1/robot-open/personal/report?page=${page}`, {
            method: "GET",
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
            },
        })
            .then((response) => response.json())
            .then((data) => {
                const chatList = document.querySelector(".chat-list");
                // Preserve the search form
                const searchForm = chatList.querySelector(".card");
                chatList.innerHTML = "";

                // Add back the search form
                if (searchForm) {
                    chatList.appendChild(searchForm);
                }

                if (!data.content || data.content.length === 0) {
                    chatList.innerHTML += `
                <div class="empty-state">
                  <i class="bi bi-chat-square-dots" style="font-size: 3rem"></i>
                  <h4 class="mt-3">No reported conversations</h4>
                  <p>There are no reported conversations at this time.</p>
                </div>`;
                    return;
                }

                data.content.forEach((message) => {
                    const chatItem = document.createElement("div");
                    chatItem.className = "chat-item";

                    const date = new Date(message.createdAt).toLocaleString("en-GB", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                    });

                    // Mask phone number if it exists in the content
                    let content = message.content;
                    // Look for phone numbers in the content and mask them
                    content = content.replace(
                        /(\+?\d{0,5})(\d+\b)/g,
                        function (match, p1, p2) {
                            // If the phone starts with +, keep the + and mask the next 5 characters
                            if (p1.startsWith("+")) {
                                return "+" + "*".repeat(Math.min(5, p1.length - 1)) + p2;
                            }
                            // Otherwise mask the first 5 characters
                            return "*".repeat(Math.min(5, p1.length)) + p2;
                        }
                    );

                    // Also mask phone numbers in the dataReport field if it exists
                    let dataReport = message.dataReport;
                    if (dataReport) {
                        dataReport = dataReport.replace(
                            /(\+?\d{0,5})(\d+\b)/g,
                            function (match, p1, p2) {
                                // If the phone starts with +, keep the + and mask the next 5 characters
                                if (p1.startsWith("+")) {
                                    return "+" + "*".repeat(Math.min(5, p1.length - 1)) + p2;
                                }
                                // Otherwise mask the first 5 characters
                                return "*".repeat(Math.min(5, p1.length)) + p2;
                            }
                        );
                    }

                    chatItem.innerHTML = `
                <div class="chat-content">
                  <p class="mb-2">${content}</p>
                  ${
                        message.audio
                            ? `
                      <audio controls style="width: 100%; height: 40px; border-radius: 20px;">
                        <source src="${message.audio}" type="audio/mpeg">
                      </audio>
                    `
                            : ""
                    }
                  <div class="d-flex justify-content-between align-items-center mt-2">
                    <span class="badge bg-danger">${
                        dataReport || message.dataReport
                    }</span>
                    <small class="text-muted">${date}</small>
                  </div>
                </div>
              `;

                    chatList.appendChild(chatItem);
                });

                if (data.totalPages > 1) {
                    const pagination = createPagination(data);
                    chatList.appendChild(pagination);
                }
            })
            .catch((error) => {
                console.error("Error:", error);
            });
    }

    function createPagination(data) {
        // Get current search parameters
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get("id");
        const phone = urlParams.get("phone");
        const userId = urlParams.get("userId");
        const startDate = urlParams.get("startDate");
        const endDate = urlParams.get("endDate");

        const nav = document.createElement("nav");
        nav.className = "d-flex justify-content-center pagination-container";

        nav.innerHTML = `
            <ul class="pagination">
                <li class="page-item ${data.first ? "disabled" : ""}">
                    <a class="page-link" href="#" onclick="loadReportedConversations(${
            data.number - 1
        }); return false;">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>

                <li class="page-item ${data.number === 0 ? "active" : ""}">
                    <a class="page-link" href="#" onclick="loadReportedConversations(0); return false;">1</a>
                </li>

                ${
            data.number > 3
                ? '<li class="page-item disabled"><span class="page-link">...</span></li>'
                : ""
        }

                ${Array.from({length: data.totalPages - 1}, (_, i) => i + 1)
            .filter(
                (i) =>
                    i > 0 &&
                    i < data.totalPages - 1 &&
                    (i <= 5 || (i >= data.number - 2 && i <= data.number + 2))
            )
            .map(
                (i) => `
                        <li class="page-item ${
                    i === data.number ? "active" : ""
                }">
                            <a class="page-link" href="#" onclick="loadReportedConversations(${i}); return false;">${
                    i + 1
                }</a>
                        </li>
                    `
            )
            .join("")}

                ${
            data.number < data.totalPages - 4
                ? '<li class="page-item disabled"><span class="page-link">...</span></li>'
                : ""
        }

                ${
            data.totalPages > 1
                ? `
                    <li class="page-item ${
                    data.number === data.totalPages - 1 ? "active" : ""
                }">
                        <a class="page-link" href="#" onclick="loadReportedConversations(${
                    data.totalPages - 1
                }); return false;">${data.totalPages}</a>
                    </li>
                `
                : ""
        }

                <li class="page-item ${data.last ? "disabled" : ""}">
                    <a class="page-link" href="#" onclick="loadReportedConversations(${
            data.number + 1
        }); return false;">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            </ul>
        `;

        return nav;
    }

    document
        .getElementById("filterReported")
        .addEventListener("click", function () {
            loadReportedConversations(0);
        });

    document
        .getElementById("filterReported")
        .addEventListener("click", function () {
            isShowingReported = !isShowingReported;
            const button = document.getElementById("filterReported");

            if (isShowingReported) {
                loadReportedConversations(0);
                button.textContent = "Show All Conversations";
                button.classList.add("btn-warning");
                button.classList.remove("btn-primary");
            } else {
                loadOriginalConversations();
                button.textContent = "Filter Reported Conversations";
                button.classList.add("btn-primary");
                button.classList.remove("btn-warning");
            }
        });

    // Add event listener for the view profile button
    document
        .getElementById("viewProfile")
        .addEventListener("click", function () {
            profileModal.show();
        });

    // Add event listener for the generate report button
    document
        .getElementById("generateReport")
        .addEventListener("click", function () {
            reportModal.show();
        });

    let isEditMode = false;
    let originalProfileData = {};

    function fetchProfileInfo() {
        const robotId = document.getElementById("robotIdInput").value.trim();
        if (!robotId) {
            alert("Please enter a Robot ID");
            return;
        }

        const profileInfoBody = document.getElementById("profileInfoBody");
        const profileLoading = document.getElementById("profileLoading");
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");

        // Clear previous content and show loading
        profileInfoBody.innerHTML = "";
        profileLoading.classList.remove("d-none");
        editBtn.style.display = "none";
        updateBtn.style.display = "none";
        isEditMode = false;

        // Fetch profile information
        fetch(`/robot/api/v1/admin/profile-variables/${robotId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Failed to fetch profile information");
                }
                return response.json();
            })
            .then((data) => {
                profileLoading.classList.add("d-none");
                if (data && data.data) {
                    const map = Object.entries(data.data);
                    if (map.length > 0) {
                        // Store original data for potential reset
                        originalProfileData = { ...data.data };

                        map.forEach((item) => {
                            const row = document.createElement("tr");
                            row.innerHTML = `
                    <td>${item[0]}</td>
                    <td class="profile-value" data-key="${item[0]}">${item[1]}</td>
                  `;
                            profileInfoBody.appendChild(row);
                        });

                        // Show edit button when data is loaded
                        editBtn.style.display = "inline-block";
                    } else {
                        profileInfoBody.innerHTML =
                            '<tr><td colspan="2" class="text-center">No profile information available</td></tr>';
                    }
                } else {
                    profileInfoBody.innerHTML =
                        '<tr><td colspan="2" class="text-center">No profile information available</td></tr>';
                }
            })
            .catch((error) => {
                profileLoading.classList.add("d-none");
                profileInfoBody.innerHTML =
                    '<tr><td colspan="2" class="text-center text-danger">Error loading profile information</td></tr>';
                console.error("Error fetching profile information:", error);
            });
    }

    function toggleEditMode() {
        const robotIdInput = document.getElementById("robotIdInput");
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");
        const profileValues = document.querySelectorAll(".profile-value");

        if (!isEditMode) {
            // Enter edit mode
            isEditMode = true;
            robotIdInput.disabled = true;
            editBtn.style.display = "none";
            updateBtn.style.display = "inline-block";

            // Make values editable
            profileValues.forEach(cell => {
                const currentValue = cell.textContent;
                const key = cell.getAttribute('data-key');
                cell.innerHTML = `<input type="text" class="form-control form-control-sm" value="${currentValue}" data-key="${key}">`;
            });
        } else {
            // Exit edit mode
            exitEditMode();
        }
    }

    function exitEditMode() {
        const robotIdInput = document.getElementById("robotIdInput");
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");
        const profileValues = document.querySelectorAll(".profile-value");

        isEditMode = false;
        robotIdInput.disabled = false;
        editBtn.style.display = "inline-block";
        updateBtn.style.display = "none";

        // Restore original values (non-editable)
        profileValues.forEach(cell => {
            const input = cell.querySelector('input');
            if (input) {
                const key = input.getAttribute('data-key');
                cell.innerHTML = originalProfileData[key] || input.value;
            }
        });
    }

    function updateProfile() {
        const robotId = document.getElementById("robotIdInput").value.trim();
        const profileInputs = document.querySelectorAll(".profile-value input");

        if (!robotId) {
            alert("Robot ID is required");
            return;
        }

        // Collect updated data
        const updatedData = {};
        profileInputs.forEach(input => {
            const key = input.getAttribute('data-key');
            updatedData[key] = input.value;
        });

        // Call API to update profile
        const updateBtn = document.getElementById("updateProfileBtn");
        const originalText = updateBtn.innerHTML;

        // Show loading state
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating...';

        fetch(`/robot/api/v1/admin/profile-variables/${robotId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updatedData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                // Success - update the original data first
                originalProfileData = { ...updatedData };

                // Exit edit mode and restore view state
                exitEditMode();

                // Show success message
                alert('Profile updated successfully!');
            } else {
                // Error from server
                alert('Error updating profile: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error updating profile:', error);
            alert('Error updating profile. Please try again.');
        })
        .finally(() => {
            // Restore button state
            updateBtn.disabled = false;
            updateBtn.innerHTML = originalText;
        });
    }

    function generateReportFromList() {
        const conversationId = document.getElementById("conversationIdInput").value.trim();
        const jsonInput = document.getElementById("reportListInput").value.trim();

        if (!conversationId) {
            alert("Please enter a Conversation ID");
            return;
        }

        if (!jsonInput) {
            alert("Please enter messages in JSON format");
            return;
        }

        // Parse the JSON input
        let messages;
        try {
            messages = JSON.parse(jsonInput);

            // Validate that it's an array
            if (!Array.isArray(messages)) {
                throw new Error("Input must be a JSON array");
            }

            // Validate that it's not empty
            if (messages.length === 0) {
                alert("Please enter at least one message");
                return;
            }

            // Validate that all items are strings
            for (const item of messages) {
                if (typeof item !== 'string') {
                    throw new Error("All items in the array must be strings");
                }
            }
        } catch (error) {
            alert(`Invalid JSON format: ${error.message}. Please enter a valid JSON array of strings.`);
            return;
        }

        const reportLoading = document.getElementById("reportLoading");
        const reportResult = document.getElementById("reportResult");
        const reportResultContent = document.getElementById("reportResultContent");
        const reportContentDisplay = document.getElementById("reportContentDisplay");
        const reportError = document.getElementById("reportError");
        const submitButton = document.getElementById("submitReportBtn");

        // Reset UI state
        reportResult.classList.add("d-none");
        reportResultContent.classList.add("d-none");
        reportError.classList.add("d-none");
        reportLoading.classList.remove("d-none");
        submitButton.disabled = true;

        // Create a form to submit the data
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/web/admin/conversation/report';
        form.target = '_blank'; // Open in a new tab
        form.style.display = 'none';

        // Add the messages as a hidden input field
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'messagesInput';
        input.value = JSON.stringify(messages);
        form.appendChild(input);

        // Add CSRF token if needed
        const csrfToken = document.querySelector('meta[name="_csrf"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_csrf';
            csrfInput.value = csrfToken.content;
            form.appendChild(csrfInput);
        }

        // Add the form to the document and submit it
        document.body.appendChild(form);

        // Show success message
        reportResult.classList.remove("d-none");
        reportResult.textContent = "Opening report in a new tab...";

        // Submit the form
        form.submit();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(form);
            reportLoading.classList.add("d-none");
            submitButton.disabled = false;

            // Close the modal after a delay
            setTimeout(() => reportModal.hide(), 1500);
        }, 1000);
    }
</script>
</body>
</html>
